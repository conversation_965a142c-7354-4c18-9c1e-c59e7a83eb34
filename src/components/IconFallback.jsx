import React, { useState, useEffect } from 'react';

/**
 * 图标降级组件
 * 当Font Awesome图标无法正常显示时，提供文本或SVG降级方案
 */
const IconFallback = ({ 
  iconClass = '', 
  fallbackText = '●', 
  fallbackSvg = null,
  size = '1em',
  color = 'currentColor',
  title = '',
  className = '',
  ...props 
}) => {
  const [shouldUseFallback, setShouldUseFallback] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // 检查Font Awesome是否正确加载
    const checkFontAwesome = () => {
      const testElement = document.createElement('i');
      testElement.className = iconClass || 'fas fa-test';
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      testElement.style.fontSize = '16px';
      
      document.body.appendChild(testElement);
      
      setTimeout(() => {
        const computedStyle = window.getComputedStyle(testElement, '::before');
        const content = computedStyle.content;
        const fontFamily = computedStyle.fontFamily;
        
        // 检查是否有Font Awesome内容或字体
        const hasFAContent = content && content !== 'none' && content !== '""';
        const hasFAFont = fontFamily && (
          fontFamily.includes('Font Awesome') || 
          fontFamily.includes('FontAwesome')
        );
        
        setShouldUseFallback(!hasFAContent && !hasFAFont);
        setIsChecking(false);
        
        document.body.removeChild(testElement);
      }, 100);
    };

    checkFontAwesome();
  }, [iconClass]);

  // 获取图标映射的降级文本
  const getIconFallbackText = (iconClass) => {
    const iconMap = {
      'fa-key': '🔑',
      'fa-user': '👤',
      'fa-lock': '🔒',
      'fa-home': '🏠',
      'fa-search': '🔍',
      'fa-download': '⬇️',
      'fa-upload': '⬆️',
      'fa-edit': '✏️',
      'fa-delete': '🗑️',
      'fa-save': '💾',
      'fa-print': '🖨️',
      'fa-email': '📧',
      'fa-phone': '📞',
      'fa-calendar': '📅',
      'fa-clock': '🕐',
      'fa-star': '⭐',
      'fa-heart': '❤️',
      'fa-check': '✓',
      'fa-times': '✕',
      'fa-plus': '+',
      'fa-minus': '-',
      'fa-arrow-left': '←',
      'fa-arrow-right': '→',
      'fa-arrow-up': '↑',
      'fa-arrow-down': '↓',
      'fa-info': 'ℹ️',
      'fa-warning': '⚠️',
      'fa-error': '❌',
      'fa-success': '✅',
      'fa-question': '❓',
      'fa-exclamation': '❗',
      'fa-cog': '⚙️',
      'fa-settings': '⚙️',
      'fa-menu': '☰',
      'fa-bars': '☰',
      'fa-close': '✕',
      'fa-file': '📄',
      'fa-folder': '📁',
      'fa-image': '🖼️',
      'fa-video': '🎥',
      'fa-music': '🎵',
      'fa-link': '🔗',
      'fa-external-link': '↗️',
      'fa-share': '📤',
      'fa-copy': '📋',
      'fa-paste': '📋',
      'fa-cut': '✂️',
      'fa-undo': '↶',
      'fa-redo': '↷',
      'fa-refresh': '🔄',
      'fa-sync': '🔄',
      'fa-loading': '⏳',
      'fa-spinner': '⏳'
    };

    // 尝试从类名中提取图标名称
    const matches = iconClass.match(/fa-([a-z-]+)/);
    if (matches && matches[1]) {
      const iconName = `fa-${matches[1]}`;
      return iconMap[iconName] || fallbackText;
    }

    return fallbackText;
  };

  // 获取SVG降级方案
  const getSvgFallback = (iconClass) => {
    const svgMap = {
      'fa-key': (
        <svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
          <path d="M7 14c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm0-4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm12.78-1.38L13.5 14.9l-1.41-1.41 6.28-6.28c.39-.39.39-1.02 0-1.41-.39-.39-1.02-.39-1.41 0L10.68 12 9.27 10.59l6.28-6.28c.78-.78.78-2.05 0-2.83-.78-.78-2.05-.78-2.83 0L6.44 7.76c-.78.78-.78 2.05 0 2.83l.71.71L5.5 13.5l2.83 2.83 2.2-2.2.71.71c.78.78 2.05.78 2.83 0l6.28-6.28c.39-.39.39-1.02 0-1.41-.39-.39-1.02-.39-1.41 0z"/>
        </svg>
      ),
      'fa-user': (
        <svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      ),
      'fa-home': (
        <svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
          <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
        </svg>
      ),
      'fa-search': (
        <svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      ),
      'fa-cog': (
        <svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
          <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
      )
    };

    const matches = iconClass.match(/fa-([a-z-]+)/);
    if (matches && matches[1]) {
      const iconName = `fa-${matches[1]}`;
      return svgMap[iconName] || null;
    }

    return null;
  };

  if (isChecking) {
    // 检查期间显示原始图标
    return (
      <i 
        className={`${iconClass} ${className}`} 
        style={{ fontSize: size, color }}
        title={title}
        {...props}
      />
    );
  }

  if (!shouldUseFallback) {
    // Font Awesome正常工作，显示原始图标
    return (
      <i 
        className={`${iconClass} ${className}`} 
        style={{ fontSize: size, color }}
        title={title}
        {...props}
      />
    );
  }

  // 需要降级方案
  const svgFallback = fallbackSvg || getSvgFallback(iconClass);
  const textFallback = getIconFallbackText(iconClass);

  if (svgFallback) {
    return (
      <span 
        className={`inline-block ${className}`}
        style={{ fontSize: size, color }}
        title={title || '图标 (SVG降级)'}
        {...props}
      >
        {svgFallback}
      </span>
    );
  }

  return (
    <span 
      className={`inline-block ${className}`}
      style={{ 
        fontSize: size, 
        color,
        fontFamily: 'Arial, sans-serif',
        lineHeight: 1,
        textAlign: 'center',
        minWidth: '1em'
      }}
      title={title || '图标 (文本降级)'}
      {...props}
    >
      {textFallback}
    </span>
  );
};

/**
 * 高阶组件：自动检测并替换页面中的Font Awesome图标
 */
export const withIconFallback = (_WrappedComponent) => {
  return function IconFallbackWrapper(props) {
    useEffect(() => {
      // 检查页面中的Font Awesome图标并添加降级方案
      const checkAndReplaceFAIcons = () => {
        const faIcons = document.querySelectorAll('.fas, .far, .fab, .fal, .fad');
        
        faIcons.forEach(icon => {
          // 检查图标是否正确显示
          const rect = icon.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(icon);
          const fontFamily = computedStyle.fontFamily;
          
          if (rect.width === 0 || rect.height === 0 || 
              (!fontFamily.includes('Font Awesome') && !fontFamily.includes('FontAwesome'))) {
            
            // 添加降级标识
            if (!icon.hasAttribute('data-fallback-applied')) {
              icon.setAttribute('data-fallback-applied', 'true');
              icon.setAttribute('data-original-class', icon.className);
              
              // 添加文本降级
              const iconClass = icon.className;
              const fallbackText = getIconFallbackText(iconClass);
              
              // 保持原有样式，但添加降级内容
              icon.innerHTML = fallbackText;
              icon.style.fontFamily = 'Arial, sans-serif';
              icon.title = icon.title || '图标 (降级显示)';
              
              console.log(`[IconFallback] 应用降级方案: ${iconClass} -> ${fallbackText}`);
            }
          }
        });
      };

      // 页面加载完成后检查
      if (document.readyState === 'complete') {
        setTimeout(checkAndReplaceFAIcons, 1000);
      } else {
        window.addEventListener('load', () => {
          setTimeout(checkAndReplaceFAIcons, 1000);
        });
      }

      // 定期检查（用于动态加载的内容）
      const interval = setInterval(checkAndReplaceFAIcons, 5000);

      return () => {
        clearInterval(interval);
      };
    }, []);

    return <WrappedComponent {...props} />;
  };
};

// 辅助函数：获取图标降级文本（与组件内部函数相同）
const getIconFallbackText = (iconClass) => {
  const iconMap = {
    'fa-key': '🔑',
    'fa-user': '👤',
    'fa-lock': '🔒',
    'fa-home': '🏠',
    'fa-search': '🔍',
    'fa-download': '⬇️',
    'fa-cog': '⚙️',
    'fa-settings': '⚙️',
    'fa-menu': '☰',
    'fa-bars': '☰'
  };

  const matches = iconClass.match(/fa-([a-z-]+)/);
  if (matches && matches[1]) {
    const iconName = `fa-${matches[1]}`;
    return iconMap[iconName] || '●';
  }

  return '●';
};

export default IconFallback;
